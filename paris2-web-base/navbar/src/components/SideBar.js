import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, Col, Modal, Row } from "react-bootstrap";
import { links } from "../modules.helper.js";
import { Link } from "react-router-dom";
import styleGuide from "../styleGuide";
import { FaUserCog } from "react-icons/fa";
import { BiTask } from "react-icons/bi";
import { RiExternalLinkLine } from "react-icons/ri";
import { AccordionMenu } from "./AccordionMenu.js";

const { Icon } = styleGuide;
const ADMINISTRATION_ROLES = {
    ITEM_MASTER_VIEW: "item-master|view",
    SHIP_PARTY_VIEW: "ship-party|view",
    HULL_AND_MACHINERY_MANAGE: "ship-party|hull-and-machinery|manage",
    PROTECTION_AND_INDEMNITY_MANAGE: "ship-party|protection-and-indemnity|manage",
    API_KEYS_VIEW: "third-party|api-key|view"
};

const EXTERNAL_USER_GROUPS = ['/Department/ExternalScreeningUsers/Employee','/Department/ExternalScreeningUsers/Supervisor','/Department/Finance/ExtM'];
export const isExternalUser = kc =>{
    if(kc.realmAccess.roles.includes('external-reporting|manage'))
        return false;  //Regardless of user group, if user has this role let him see the external-reporting module
    const userGroups = kc?.tokenParsed?.group || [];
    const isPartOfExternalGroups = userGroups.some(group => {
        return group.startsWith('/External/') || EXTERNAL_USER_GROUPS.includes(group);
    }); // Users belonging to above groups are not inhouse users and should be regarded as external users
    return isPartOfExternalGroups;
}
export const hasRole = kc => ({ role }) => {
  if (typeof role === "string"){
    if (role === "*" && !isExternalUser(kc)) {
      return true
    }
    return kc.realmAccess.roles.includes(role);
  }
  if (Array.isArray(role))
    return role.some(role => kc.realmAccess.roles.includes(role));
  return false;
};
const isOwner = keycloak =>
  keycloak.tokenParsed.ship_party_id &&
  keycloak.tokenParsed.ship_party_type === "Owner";
// Always show admin option in sidebar for External Reporting
const hasAdminRoles = keycloak => 
    keycloak.realmAccess.roles.some(role =>
        Object.values(ADMINISTRATION_ROLES).includes(role)
    ) || !isExternalUser(keycloak);
const hasAuditInspectionRoles = keycloak =>
  keycloak.realmAccess.roles.some(
    role => role.startsWith("deficiency|") || role.startsWith("inspection|")
  );
const hasNovaOrAnalyticsRoles = keycloak =>
  keycloak.realmAccess.roles.some(
    role => role.startsWith("nova|") || role.startsWith("anl|") || role.startsWith("dap|")
  );
const hasPortageBillRoles = keycloak =>
  keycloak.realmAccess.roles.some(role => role.startsWith("pb|"));
const hasVesselRoles = keycloak =>
  keycloak.realmAccess.roles.some(role => role.startsWith("vessel|"));
const hasSeafarerRoles = keycloak =>
  keycloak.realmAccess.roles.some(role => role.startsWith("seafarer|")|| role.startsWith('news|'));
const hasSurveyRoles = keycloak =>
  keycloak.realmAccess.roles.some(role => role.startsWith("survey|"));
const hasQHSERoles = keycloak =>
  keycloak.realmAccess.roles.some(role => role.startsWith("qhse|"));
const hasQHSEForRAMRoles = keycloak =>
  keycloak.realmAccess.roles.some(role => role === "ra|temp|view");
const hasVettingRole = keycloak =>
  !!keycloak.realmAccess.roles.find(role => role === "inspection|vetting|view");
const hasFileTransferRoles = keycloak =>
  keycloak.realmAccess.roles.some(role => role.includes("file-transfer|view"));
const hasParis1linkRole = keycloak =>
  keycloak.realmAccess.roles.some(role => role === "paris1links|view");
const hasReferenceRole = keycloak =>
  keycloak.realmAccess.roles.some(role => role === "general|view|reference");
const hasWorldMapRoles = keycloak => true;
const hasPmsRoles = (keycloak) =>  keycloak.realmAccess.roles.some((role) => role === 'pms|view');
const hasNewBuildingRoles = keycloak =>
    keycloak.realmAccess.roles.some(role => role.startsWith("nbp|"));

const SideBar = ({ kc, currentPath, ga4EventTrigger, visible = false }) => {
  const { PARIS_ONE_HOST } = process.env;
  const [show, setShow] = useState(visible);

  useEffect(() => {
    setShow(visible);
  }, [visible]);

  const handleClose = () => {
    setShow(false);
  };
  const handleShow = () => {
    setShow(true);
  };

  const novaTitle = (
    <span id="sidebar-link-nova-data-insight" className="nova-data-insight">
      NOVA
    </span>
  );

  const menuLink = element => (
    <Col
      xs={12}
      xl={12}
      className={`sidenav-menu-item ${
        currentPath === element.href ? "active-link" : ""
      }`}
    >
    <div className="static_sidenav_btn" >
      <Link
        key={element.href}
        className="p-6 side-nav-link"
        to={element.href}
        onClick={handleClose}
      >
        <Button className="btn-sidenav__module border-0" block>
          {element.icon && (
            <Icon
              icon={element.icon}
              size={30}
              className="sidebar_navigation__icon text-primary"
            />
          )}
          {element.title}
        </Button>
      </Link>
      </div>
    </Col>
  );

  return (
    <>
      <Icon
        id="sidebar-menu-icon"
        data-testid="sidebar-menu-icon"
        icon="modules"
        size={20}
        color={"white"}
        onClick={handleShow}
        className="main-menu__icon"
      />
      <Modal
        className="sideBar__modal"
        show={show}
        onHide={handleClose}
        id="sidebar-modal-layout"
        data-testid="sidebar-modal-layout"
      >
        <Modal.Header data-testid="sidebar-header">
          <Modal.Title>
            <Icon
              icon="modules"
              size={20}
              onClick={handleClose}
              className="main-menu__icon__subnav text-primary"
              data-testid="sidebar-title-menu-icon"
              id="sidebar-menu-icon"
            />
          </Modal.Title>
        </Modal.Header>
        <Modal.Body className="sidenav-modal-body">
          <Row className="mx-0">
            <div className="w-100 ">
              {menuLink(links.home)}

              {hasWorldMapRoles(kc) && menuLink(links.paris20_worldmap_modules)}

              {hasVesselRoles(kc) && (
                <AccordionMenu
                  id="vessel"
                  links={links.paris20_vessel_modules.filter(hasRole(kc))}
                  ParentIcon={
                    <Icon
                      icon={"vessel-invert"}
                      size={30}
                      className="sidebar_navigation__icon text-primary"
                    />
                  }
                  title="Vessel"
                  currentPath={currentPath}
                  onClick={handleClose}
                  keycloak={kc}
                />
              )}
              {hasSeafarerRoles(kc) &&
                links.paris20_seafarer_modules.filter(hasRole(kc)).length >
                  0 && (
                  <AccordionMenu
                    id="seafarer"
                    links={links.paris20_seafarer_modules.filter(hasRole(kc))}
                    ParentIcon={
                      <Icon
                        icon={"crew-invert"}
                        size={30}
                        className="sidebar_navigation__icon text-primary"
                      />
                    }
                    title="Seafarer"
                    currentPath={currentPath}
                    onClick={handleClose}
                    keycloak={kc}
                  />
                )}
              {hasSurveyRoles(kc) && menuLink(links.paris20_survey_modules)}
              {(hasQHSERoles(kc) || hasQHSEForRAMRoles(kc)) && (
                <AccordionMenu
                  id="qhse"
                  links={links.paris20_qhse_modules.filter(hasRole(kc))}
                  ParentIcon={
                    <Icon
                      color="white"
                      icon="qhse"
                      size={30}
                      className="sidebar_navigation__icon analytics-icon text-primary"
                    />
                  }
                  title={"QHSE"}
                  currentPath={currentPath}
                  onClick={handleClose}
                  keycloak={kc}
                />
              )}
              {hasAuditInspectionRoles(kc) && (
                <AccordionMenu
                  id="inspection"
                  links={
                    hasVettingRole(kc)
                      ? [links.paris20_inspection_audit_modules[0]]
                      : links.paris20_inspection_audit_modules
                  }
                  ParentIcon={
                    <BiTask
                      size={30}
                      className="sidebar_navigation__icon administration-icon inspection-icon"
                    />
                  }
                  title="Inspection/Audit"
                  currentPath={currentPath}
                  onClick={handleClose}
                  keycloak={kc}
                />
              )}
              {hasNewBuildingRoles(kc) &&
                links.paris20_new_building_modules.filter(hasRole(kc)).length >
                  0 && (
                  <AccordionMenu
                    id="new_building"
                    links={links.paris20_new_building_modules.filter(hasRole(kc))}
                    ParentIcon={
                      <Icon
                        icon={"NewBuilding"}
                        size={30}
                        className="sidebar_navigation__icon text-primary"
                      />
                    }
                    title="New Building"
                    currentPath={currentPath}
                    onClick={handleClose}
                    keycloak={kc}
                  />
                )}
              {hasPortageBillRoles(kc) && (
                <AccordionMenu
                  id="finance"
                  links={links.paris20_finance_modules.filter(hasRole(kc))}
                  ParentIcon={
                    <Icon
                      icon="finance"
                      size={30}
                      className="sidebar_navigation__icon finance-icon"
                    />
                  }
                  title="Finance"
                  currentPath={currentPath}
                  onClick={handleClose}
                  keycloak={kc}
                />
              )}
              {hasAdminRoles(kc) && (
                <AccordionMenu
                  id="administration"
                  links={links.paris20_administration_modules.filter(
                    hasRole(kc)
                  )}
                  ParentIcon={
                    <FaUserCog
                      size={30}
                      className="sidebar_navigation__icon administration-icon"
                    />
                  }
                  title="Administration"
                  currentPath={currentPath}
                  onClick={handleClose}
                  keycloak={kc}
                />
              )}
              {hasPmsRoles(kc) && (
                <AccordionMenu
                  id="pms"
                  links={links.paris20_pms_modules}
                  ParentIcon={
                    <Icon
                      icon={"spanner"}
                      size={30}
                      className="sidebar_navigation__icon text-primary"
                    />
                  }
                  title="PMS"
                  currentPath={currentPath}
                  onClick={handleClose}
                  keycloak={kc}
                />
              )}
              {hasFileTransferRoles(kc) && (
                <AccordionMenu
                  id="file_transfer"
                  links={links.paris20_file_transfer_modules}
                  ParentIcon={
                    <Icon
                        icon={"file-transfer-icon-invert"}
                        size={30}
                        className="sidebar_navigation__icon text-primary"
                    />
                  }
                  title="File Transfer"
                  currentPath={currentPath}
                  onClick={handleClose}
                  keycloak={kc}
                />
              )}
              {hasNovaOrAnalyticsRoles(kc) && (
                <AccordionMenu
                  id="nova"
                  links={links.paris20_nova_modules.filter(hasRole(kc))}
                  ParentIcon={
                    <Icon
                      color="white"
                      icon="NOVA"
                      size={30}
                      className="sidebar_navigation__icon analytics-icon text-primary"
                    />
                  }
                  title={novaTitle}
                  currentPath={currentPath}
                  onClick={handleClose}
                  keycloak={kc}
                />
              )}
              {hasReferenceRole(kc) &&
                menuLink(links.paris20_reference_modules)}
              {hasParis1linkRole(kc) && (
                <Col xs={12} xl={12} className={"sidenav-menu-item"}>
                  <a
                    key="paris_one"
                    className="p-6"
                    href={
                      isOwner(kc)
                        ? `${PARIS_ONE_HOST}/fml/FMLLoginKeycloak?targeturl=/fml/PARIS`
                        : `${PARIS_ONE_HOST}/fml/PARIS`
                    }
                    target="_blank"
                    onClick={handleClose}
                  >
                    <Button className="btn-sidenav__module border-0" block>
                      <RiExternalLinkLine
                        size={30}
                        className="sidebar_navigation__icon administration-icon"
                      />
                      <span id="sidebar-link-paris1">PARIS 1.0</span>
                    </Button>
                  </a>
                </Col>
              )}
            </div>
          </Row>
        </Modal.Body>
      </Modal>
    </>
  );
};
export default SideBar;
