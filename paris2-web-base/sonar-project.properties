sonar.projectKey=paris2-web-base
sonar.projectName=paris2-web-base
sonar.sourceEncoding=UTF-8
sonar.sources=navbar/src/,notifications/src/,auth/src/,faq/src/,external-config/src/,landing-page/src/,localization/src/,notification-events/src/,root-config/src/,style-guide/src/
sonar.coverage.exclusions=*/src/coverage/**/*,**/__tests__/**,**/__test__/**,*.test.*
sonar.cpd.exclusions=**/__tests__/**,**/__test__/**,*.test.*,**/__mocks__/**,**/__config__/**
sonar.javascript.coveragePlugin=lcov
sonar.javascript.lcov.reportPaths=notifications/src/coverage/lcov.info,navbar/src/coverage/lcov.info,landing-page/src/coverage/lcov.info
