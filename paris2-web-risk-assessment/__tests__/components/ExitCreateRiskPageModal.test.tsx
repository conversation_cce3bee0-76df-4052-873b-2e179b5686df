import React from 'react';
import {render, screen, fireEvent} from '@testing-library/react';
import {ExitCreateRiskPageModal} from '../../src/components/ExitCreateRiskPageModal';
import * as router from 'react-router-dom';

// Mock useNavigate
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

describe('ExitCreateRiskPageModal', () => {
  const mockOnClose = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render modal with correct content', () => {
    render(<ExitCreateRiskPageModal onClose={mockOnClose} />);

    // Check modal title
    expect(
      screen.getByText('Exit RA Creation without Saving'),
    ).toBeInTheDocument();

    // Check warning message
    expect(
      screen.getByText(
        /Are you sure you want to exit without saving this Risk Assessment as a draft\?/,
      ),
    ).toBeInTheDocument();
    expect(
      screen.getByText(/All entered information will be lost permanently\./),
    ).toBeInTheDocument();

    // Check buttons
    expect(screen.getByText('Discard RA')).toBeInTheDocument();
    expect(screen.getByText('Keep Editing')).toBeInTheDocument();
  });

  it('should call navigate when "Discard RA" button is clicked', () => {
    render(<ExitCreateRiskPageModal onClose={mockOnClose} />);

    const discardButton = screen.getByText('Discard RA');
    fireEvent.click(discardButton);

    expect(mockNavigate).toHaveBeenCalledWith('/risk-assessment');
  });

  it('should call onClose when "Keep Editing" button is clicked', () => {
    render(<ExitCreateRiskPageModal onClose={mockOnClose} />);

    const keepEditingButton = screen.getByText('Keep Editing');
    fireEvent.click(keepEditingButton);

    expect(mockOnClose).toHaveBeenCalled();
  });

  it('should have correct data-testid for error alert', () => {
    render(<ExitCreateRiskPageModal onClose={mockOnClose} />);

    expect(screen.getByTestId('error-alert')).toBeInTheDocument();
  });
});
