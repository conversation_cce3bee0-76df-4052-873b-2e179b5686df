module.exports = {
  rootDir: '__tests__',
  testEnvironment: 'jsdom',
  testMatch: ['**/*.test.ts', '**/*.test.tsx'],
  transform: {
    '^.+\\.(j|t)sx?$': 'babel-jest',
  },
  moduleNameMapper: {
    '\\.(css)$': 'identity-obj-proxy',
    'single-spa-react/parcel': 'single-spa-react/lib/cjs/parcel.cjs',
    '^.+\\.(css|less|scss)$': 'babel-jest',
    '\\.(pdf)$': '<rootDir>/__mocks__/fileMock.js',
    '@paris2/auth': '<rootDir>/__mocks__/paris2-auth.ts',
    '@paris2/styleguide': '<rootDir>/__mocks__/paris2-styleguide.ts',
    axios: '<rootDir>/__mocks__/axios.ts',
  },
  setupFilesAfterEnv: [
    '@testing-library/jest-dom',
    '<rootDir>/__mocks__/paris2-auth.ts',
  ],
  transformIgnorePatterns: ['node_modules/(?!axios)'],
  testTimeout: 10000,
  coveragePathIgnorePatterns: ['/node_modules/', 'src/enums/'],
};
