{"env": "uat2", "aws_account_id": "************", "region": "ap-southeast-1", "paris1": {"host": "https://parisuat.fleetship.com"}, "web_app": {"website_s3_bucket": "paris2-website-uat2", "base_url": "https://paris2-uat2.fleetship.com", "script_base_url": "https://paris2-uat2.fleetship.com", "cdn_base_url": "/npm-cdn", "share_point_cms_site_name": "PARIS20-CMS", "rss_url": "https://www.fleetship.com/feed", "inspections_audits_base": "inspections-audits"}, "api": {"endpoint_type": "edge", "base_urls": {"vessel": "https://paris-api-uat2.fleetship.com/vessel", "keycloak": "https://paris-api-uat2.fleetship.com/keycloak-admin", "notification": "https://notification-uat2.fleetship.com", "auth": "https://auth-uat2.fleetship.com/auth", "reference": "https://paris-api-uat2.fleetship.com/reference", "vessel_accounting_facade": "https://paris2-owner-financial-reporting-api-uat2.fleetship.com/vesselaccounting-facade", "risk_assessment": "https://paris-api-uat2.fleetship.com/risk-assessment", "ship_party": "https://paris-api-uat2.fleetship.com/ship-party", "tableau_proxy": "https://paris-api-uat2.fleetship.com/tableau", "seafarer": "https://paris-api-uat2.fleetship.com/seafarer", "data_import": "https://dataimport-uat2.fleetship.com/api/data-import", "new_building_service": "https://paris2-new-building-api-uat2.fleetship.com/api", "file": "https://paris-api-uat2.fleetship.com/file", "inspection_hasura": "https://hasura-qms2-api-uat2.fleetship.com/v1/graphql", "inspection_lambda": "https://paris-api-uat2.fleetship.com/hasura-action-api", "inspection_create": "https://i4llz3z5ga5fg6akigf6isvvom0iyptv.lambda-url.ap-southeast-1.on.aws", "app_distribution": "https://paris-api-uat2.fleetship.com/app-distribution", "deficiency_hasura": "https://hasura-defects-api-uat2.fleetship.com/v1/graphql", "wss_base": "wss://hasura-qms2-api-uat2.fleetship.com/v1/graphql", "deficiency_ws": "wss://hasura-defects-api-uat2.fleetship.com/v1/graphql", "inspection_deficiency_reference": "https://paris-api-uat2.fleetship.com/inspection-deficiency-reference", "base_images_qmsv2": "https://paris-api-uat2.fleetship.com/qmsv2-images", "base_images_qmsv2_files": "https://paris-api-uat2.fleetship.com/qmsv2-images/files", "crew_assignment": "https://paris-api-uat2.fleetship.com/crew-assignment", "survey": "https://paris-api-uat2.fleetship.com/survey", "qhse": "https://paris-api-uat2.fleetship.com/qhse", "deficiencies": "https://paris-api-uat2.fleetship.com/deficiencies", "item_master": "https://paris-api-uat2.fleetship.com/item-master", "owner_reporting_s3": "https://paris2-ofr-uat2.s3.ap-southeast-1.amazonaws.com", "paris_api": "https://paris-api-uat2.fleetship.com/", "inspection": "https://paris-api-uat2.fleetship.com/inspection", "defects": "https://paris-api-uat2.fleetship.com/defects", "portagebill_facade": "https://paris2-portagebill-api-uat2.fleetship.com/portagebill-facade", "account": "https://paris-api-uat2.fleetship.com/account", "etl_facade": "https://paris2-portagebill-api-uat2.fleetship.com/etl-facade", "external_reporting_url": "https://paris2-external-reporting-uat2.fleetship.com/etl-service", "inventory_management": "https://paris-api-uat2.fleetship.com/inventory-management"}, "domain_name": "paris-api-uat2.fleetship.com"}, "ecr": {"registry": "************.dkr.ecr.ap-southeast-1.amazonaws.com", "repositories": {"tableau_proxy": "************.dkr.ecr.ap-southeast-1.amazonaws.com/paris2-tableau-proxy", "vessel_spare_sync": "************.dkr.ecr.ap-southeast-1.amazonaws.com/paris2-vessel-spare-sync", "data_import": "************.dkr.ecr.ap-southeast-1.amazonaws.com/paris2-data-import", "requisition": "************.dkr.ecr.ap-southeast-1.amazonaws.com/paris2-requisition", "notification": "************.dkr.ecr.ap-southeast-1.amazonaws.com/paris2-notification"}, "tags": {"keycloak": "9.0.3-v4", "notification": "uat2-40"}}, "tableau": {"host": "https://tableau-uat2.fleetship.com", "proxy_host": "https://tableau-proxy-uat2.fleetship.com", "proxy_uuid_namespace": "80d6d0b8-0fe2-4573-a41c-7eb95f02d837", "proxy_auth_image_tag": "nova-authServer-uat2-53", "proxy_web_image_tag": "nova-webServer-uat2-53"}, "notification": {"db_host": "paris2-vessel-uat2.cnokqrjedngk.ap-southeast-1.rds.amazonaws.com", "jaeger_base_url": "https://jaeger-common-collector.fleetship.com/api/traces", "host": "notification-uat2.fleetship.com", "ingress_group_name": "common-alb", "cors_origin": "https://paris2-uat2.fleetship.com"}, "file": {"s3_buckets": {"file_transfer": "paris2-file-transfer-uat2", "vessel": "paris2-vessel-files-uat2", "file": "paris2-files-uat2", "static": "paris2-static-uat2", "inspection_file": "paris2-inspection-file-upload-uat2", "inspection_image": "paris2-inspection-image-upload-uat2", "inspection_image_upload_event": "paris2-inspection-image-upload-event-uat2", "inspection_image_upload_lz4_event": "paris2-inspection-image-upload-lz4-event-uat2", "inspection_xls_upload": "paris2-inspection-docs-upload-uat2", "inspection_xls_download": "paris2-inspection-docs-download-xls-uat2", "inspection_mail": "paris2-inspection-mail-uat2", "inspection_payload": "paris2-inspection-payloads-uat2", "seafarer_files": "paris2-seafarer-files-uat2", "inventory_management_upload": "paris2-inventory-management-upload-uat2"}, "efs": {"local_mount_path": "/mnt/lambda-efs", "access_point_arn": "arn:aws:elasticfilesystem:ap-southeast-1:684031101155:access-point/fsap-0b9cc7607641bda53", "file_system_arn": "arn:aws:elasticfilesystem:ap-southeast-1:684031101155:file-system/fs-6cb5af2d"}, "token": {"upload": {"jwt_public_key": "-----B<PERSON>IN PUBLIC KEY-----\nMIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDdMHX/FvuXbbFndztheOBXQfyfpeeuE+wJr4IrIqJxWs8zSssJ2LYbq7S/J4CNoawAE6ee4wwPdLrO5WY5zfTc5BZSR6d1JIaJxzXUv+YwNekiOm9XYLhlhyqIeS6NYdnGli5WQN89aIJhEPbCb4bOXyKXAZVMuEsWTy6qkKeDcQIDAQAB\n-----END PUBLIC KEY-----", "expire_in": 300}, "download": {"expire_in": 900}}}, "network": {"vpc_id": "vpc-01815c546a44b855a", "lambda_vpc_config": {"securityGroupIds": ["sg-0756f6b9fe1017db1"], "subnetIds": ["subnet-0f1b715ad3eccd973", "subnet-0fcb5d300240fa26d", "subnet-013eb0dbb339a124a"]}}, "auth": {"jwt_public_key": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAjkw7R3AW/F6aWl/VdMtlr3Tw50xkodSvUw3c8d8OmTI5XoWDIQxQNXH4JyRd5YhZq2Jn9018qDuXhEJHfWkkfvy6iaxrdAO2grFeZMsSOBvLWMKnFVcZ8uBG3oCAUmswBlDx3zS9WrmGF3SbNqVY1DUTiu4OrHC9tR0iBfvMlyp3JkrnxKOw5xllcUP+WmIgxTpMRZypovYAqrFyxYfBDiEAcBDPDDKgmn0DlE9fU19Dqa8+1FEjemZd9TEeV2dp7/HBgdFySxNYdQDl8fTBKHJFbmXFWyC18OQly8EuVxsW38EN97cb3wy+DF3WIF1DfbxYeEiIuB8uUceD1yBeBQIDAQAB\n-----END PUBLIC KEY-----", "host": "https://auth-uat2.fleetship.com", "domain_name": "auth-uat2.fleetship.com", "keycloak_realm": "paris2", "client_id": "paris2-app", "ingress_group_name": "common-alb", "db_host": "paris2-keycloak-uat2.cnokqrjedngk.ap-southeast-1.rds.amazonaws.com", "azure_ad_app_client_id": "6abc9716-211e-402c-8a4a-b95d1b6c44f1", "azure_ad_token_host": "https://login.microsoftonline.com/cf7f83ec-38ea-406d-b1df-71df17fe5c00/oauth2/v2.0/token", "sns_topic": "arn:aws:sns:ap-southeast-1:************:paris2-keycloak-assign-event-uat2"}, "log": {"log_level": "debug", "logstash": {"host": "logstash-uat2.fleetship.com"}}, "api_gateway_execution_log": {"defects_api_group": "API-Gateway-Execution-Logs_eizz87oubl/uat2", "photo_api_group": "API-Gateway-Execution-Logs_z12jfsh34d/uat2"}, "data_import": {"host": "dataimport-uat2.fleetship.com", "ingress_group_name": "common-alb", "image_tag": "dataimport-uat2-1", "db_connection_key_id": "46c412c2-f4ba-414d-a516-df59b0ba9a11", "deployed_date": "1,637,804,783,033", "dynamodb_table_name_dataset": "paris2-data-import-service-uat2-DatasetB87AF082-1W8ZTZ76TRJ2Q", "dynamodb_table_name_import_status": "paris2-data-import-service-uat2-ImportStatus08B8789E-1NX5Y3QVTSFE", "dynamodb_table_name_schema": "paris2-data-import-service-uat2-Schema42171392-ZSBHVNEJPNVR", "dynamodb_table_name_version": "paris2-data-import-service-uat2-Version6A868472-1PKTZ6I32NKFK"}, "requisition": {"minimum_receive_date": "2022-08-01T00:00:00Z", "feedback_trace_back_time": 744, "requisition_sync_task_tag": "purchase-receipts-from-p1-dev2-65", "purchase_receipt_sync_task_url": "http://a5e2b5a15bc854636930bc346e694e54-b6fac08619ec3c16.elb.ap-southeast-1.amazonaws.com:3000"}, "seafarer": {"enable_create_update_seafarer": true, "date_validate_all_seamanbook": "2022-03-21 14:00:00.000 +0800", "scoring_api": "https://u3ymu4q9sd-vpce-0ff2ed39394459508.execute-api.ap-southeast-1.amazonaws.com/dev/crew-scoring", "ocimf_sync_task_url": "http://a6a78e711c527405b83271ed2fa0b668-**********.ap-southeast-1.elb.amazonaws.com:3000", "ocimf_sire_wsdl_url": "https://staging-ws.ocimf.biz/OcimfServices.asmx?WSDL"}, "third_party": {"support_email": ""}, "external_services": {"fos": {"api_base_url": "https://accounts-stage.fos.transas.com/api/v3", "wartsila_host": "https://fos-test.eniram.fi", "vessel_hardware_poll": "cron(15 1 * * ? *)", "role_id_fleet_viewer": "2bc49a9508d8443d9ab6dd0427c57f72", "client_id": "b93676ac-e0be-49b5-bf7b-be3d32370e28", "authority": "https://login.microsoftonline.com/a9910275-d712-421f-9de7-621fa821db06", "scope": "4d217f9f-2647-4c89-90a2-90c2edd817f8/.default", "username": "<EMAIL>"}, "azure_ad": {"owner_client_id": "4d12072b-2ecf-40d5-bd5a-ef8157465ad8", "owner_tenant_id": "********-8980-4b57-bfde-2e85c69f9ee6"}, "google_analytics": {"measurement_id": "UA-*********-1", "ga_code": "G-QFH9JC1YMM", "ua_code": "UA-*********-5", "gtm_code": "GTM-KJDXLH9"}, "google_map": {"api_key": "AIzaSyDR_SeN4OQ7HgnbFZoKrDD75Qp1cBCcPso"}, "stratum_five": {"api_base_url": "https://system.stratumfive.com"}, "firebase": {"inspection": {"auth_domain": "vessel-inspection-report.firebaseapp.com", "project_id": "vessel-inspection-report", "storage_bucket": "vessel-inspection-report.appspot.com", "messaging_sender_id": "594778280952", "api_id": "1:594778280952:web:50c3654838218bf6d6fc0d", "measurement_id": "G-Y6W3YH24Q7", "api_key": "AIzaSyAGxNxI_bW2Cg1ru4B0BO7rOCe2uJSxbDI"}}, "share_point": {"domain_name": "fleetship.sharepoint.com", "tenant_id": "cf7f83ec-38ea-406d-b1df-71df17fe5c00", "grant_type": "client_credentials", "resource": "00000003-0000-0ff1-ce00-000000000000"}, "world_check_one": {"protocol": "https://", "host": "api-worldcheck.refinitiv.com", "base_path": "/v2/", "group_id": "5jb69ekr1m1p1fkg2n38vzyuq", "db_write_capacity": 10}, "ship_data_center": {"nk_verifier_api": "https://tri-api.shipdatacenter.com/ios-op/v1/data-import/file-data"}, "azure_websites": {"abs_verifier_api": "https://uat-mrv-api.azurewebsites.net/api/ImportReport"}, "dnv": {"ovd_admin_host": "https://ovdadmin.veracityapp.com", "ovd_veracity_app_host": "https://ovd.veracityapp.com/api"}, "bloomberg": {"api_url_base": "https://api.bloomberg.com/eap/catalogs"}, "jenkins": {"url": "https://jenkins-common.fleetship.com"}}, "schedule": {"cron_expression": {"15_mintutes": "rate(15 minutes)", "requisitions_sync_pilot_vessel_from_paris1": "cron(0 1 * * ? *)", "vessel_sync_default": "rate(15 minutes)", "requisitions_sync_from_paris1": "rate(30 minutes)", "requisitions_notify_changes": "cron(15 1 * * ? *)", "requisitions_sync_purchase_receipts_from_paris1": "cron(0 5,11,23 * * ? *)", "seafarer_sync_paris1_seafarer_cron_schedule": "cron(15 1 * * ? *)", "seafarer_sync_paris1_seafarer_dropdown_cron_schedule": "cron(14 1 * * ? *)", "seafarer_seafarer_automatic_archival_schedule": "cron(0 * * * ? *)", "seafarer_refresh_experience_summary_mv_cron_schedule": "cron(0 1 * * ? *)", "seafarer_sync_seafarer_status_cron_schedule": "cron(0-59/3 * * * ? *)", "seafarer_sync_seafarer_experience_cron_schedule": "cron(0-59/15 * * * ? *)", "crew_assignment_sync_payheads_paris2_to_paris1_cron_schedule": "cron(0 1 * * ? *)", "crew_assignment_sync_crew_assignment_details_cron_schedule": "cron(0-59/10 * * * ? *)", "crew_assignment_apply_wage_update_and_promotion_schedule": "cron(15 * * * ? *)", "crew_assignment_populate_new_vessel_ownership_cron_schedule": "cron(0-59/10 * * * ? *)", "crew_assignment_sync_master_appraisal_cron_schedule": "cron(0-59/10 * * * ? *)", "crew_assignment_admin_report_cron_schedule": "cron(55 15 * * ? *)", "crew_assignment_supy_recommendation_approval_cron_schedule": "rate(5 minutes)", "crew_assignment_travel_agents_cron_schedule": "cron(* * * * ? *)", "crew_assignment_prepare_crewlist_report_data_cron_schedule": "rate(2 minutes)", "crew_assignment_prepare_crewlist_report_sheet_cron_schedule": "rate(3 minutes)", "crew_assignment_ocimf_cron_schedule": "cron(0 12 * * ? *)", "crew_assignment_mark_crew_plan_expire_cron_schedule": "cron(45 1 * * ? *)", "survey_populate_access_tables_cron_schedule": "cron(5 1 * * ? *)", "survey_offboarding_cron_schedule": "cron(5 1 * * ? *)", "survey_debriefing_cron_schedule": "cron(5 1 * * ? *)", "sync_inspection_dropdown": "cron(15 1 * * ? *)", "sync_hourly_working_days": "cron(0/60 * ? * MON-FRI *)", "sync_active_deficiency": "cron(0/15 * ? * MON-FRI *)", "sync_pms": "cron(0 0 * * ? *)", "sync_position_report": "rate(15 minutes)", "sync_position_report_luboil_consumption": "cron(0 0 * * ? *)", "sync_osc_inventory_balance_update": "cron(0 0 * * ? *)", "sync_requisition": "rate(30 minutes)", "notify_requisition_status_changes": "cron(15 1 * * ? *)", "notify_rejected_inventory_update": "cron(15 1 * * ? *)", "sync_itinerary": "rate(15 minutes)", "vessel_navigational_equipment_sync_cron_schedule": "cron(15 1 * * ? *)", "inventory_management_sfi_master_paris2_sync_cron_schedule": "cron(15 1 * * ? *)", "inventory_management_sfi_master_paris1_sync_cron_schedule": "cron(30 1 * * ? *)", "sire_ocimf_matrix_sync_cron_schedule": "cron(0 1 ? * 7 *)", "precompute_ocimf_compliance_cron_schedule": "cron(0 19 ? * * *)"}}, "kms": {"keys": {"ssm": "key/2e41c50f-b8ba-40e1-a9a5-44ed1aad1ddb"}}, "params": {}, "emails": {"inspection_approval_source": "<EMAIL>", "eu_ets_email_failure_recipients": "<EMAIL>/<EMAIL>", "eu_voyage_alert_email_cc": "<EMAIL>/<EMAIL>/<EMAIL>", "report_to_admin_failure_email": "<EMAIL>", "report_to_admin_from_email": "<EMAIL>", "report_to_admin_to_email": "<EMAIL>,<EMAIL>,<EMAIL>", "report_to_admin_cc_email": "<EMAIL>,<EMAIL>", "inventory_management_import_failure_cc": "<EMAIL>"}, "sqs": {"inventory_management_import_queue": "paris2-inventory-management-upload-uat2.fifo", "inventory_management_p1_sync_queue": "paris2-inventory-management-p1-sync-uat2.fifo"}, "kubernetes": {"ingress": {"alb_group_name": "common-alb"}, "replica_count": {"hasura_defects": 1, "hasura_qms": 1}}, "credentials_params": {"paris1_db_connection": "/paris1-db-connection/uat2", "paris1_db_password": "/paris1-db-password/uat2", "paris2_deficiency_db_connection": "/paris2-db-connection/deficiency/uat2", "paris2_inspection_db_connection": "/paris2-db-connection/inspection/uat2", "paris2_item_master_db_connection": "/paris2-db-connection/item_master/uat2", "paris2_ship_party_db_connection": "/paris2-db-connection/ship_party/uat2", "paris2_external_reporting_db_connection": "/paris2-db-connection/external-reporting/uat2", "paris2_owner_financial_reporting_db_connection": "/paris2-db-connection/owner-financial-reporting/uat2", "paris2_auth_db_connection": "/paris2-auth-db-connection/uat2", "paris2_vessel_db_connection": "/paris2-db-connection/vessel/uat2", "paris2_control_plane_db_host": "/paris2-control-plane-db-host/uat2", "paris2_control_plane_db_password": "/paris2-control-plane-db-password/uat2", "paris2_control_plane_db_connection": "/paris2-db-connection/control-plane/uat2", "paris2_notification_db_connection": "/paris2-db-connection/notification/uat2", "paris2_vessel_db_password": "/paris2-vessel-db-password/uat2", "paris2_vessel_service_user": "/paris2-api-vessel-user/uat2", "paris2_item_master_user": "/paris2-api-item-master-user/uat2", "paris2_ship_party_user": "/paris2-api-ship-party-user/uat2", "paris2_service_client_secret": "/paris2-service-client-secret/uat2", "paris2_keycloak_password": "/paris2-auth-admin-password/uat2", "sonar_auth_token": "/paris2-sonar-auth-token/uat2", "keycloak_admin_user": "/paris2-api-keycloak-user/uat2", "stratum_five_api_credential": "/paris2-stratum-five-api-credential/uat2", "paris2_survey_db_connection": "/paris2-db-connection/survey/uat2", "paris2_seafarer_db_connection": "/paris2-db-connection/seafarer/uat2", "paris2_portagebill_db_connection": "/paris2-db-connection/portagebill/uat2", "paris2_seafarer_db_password": "/paris2-seafarer-db-password/uat2", "paris2_auth_db_password": "/paris2-auth-db-password/uat2", "paris2_service_user": "/paris2-service-auth-user/uat2", "username_hash_key": "/auth-username-hash-secret-key/uat2", "paris2_qhse_db_connection": "/paris2-db-connection/qhse/uat2", "paris2_qhse_user": "/paris2-api-qhse-user/uat2", "auth_admin_client_secret": "/paris2/auth/admin-client-secret/uat2", "hasura_inspection_admin_secret": "/paris2-hasura-inspections-admin-secret/uat2", "hasura_defects_admin_secret": "/paris2-hasura-defects-admin-secret/uat2", "world_check_one_api_key": "/world-check-one-api-key/uat2", "world_check_one_api_secret": "/world-check-one-api-secret/uat2", "paris2_inspection_service_user": "/paris2-api-inspection-user/uat2", "paris2_deficiency_sync_service_user": "/paris2-api-deficiency-sync-user/uat2", "oracle_finance_db_connection": "/oracle-financial-db-connection/uat2", "nk_verifier_api_credential": "/paris2-nk-verifier-api-credential/uat2", "abs_api_access_token": "/paris2-abs-api-access-token/uat2", "third_party_integration_ip_set": "/paris2-third-party-integration-ip-set/uat2", "api_key_client_secret": "/paris2-api-key-keycloak-client-secret/uat2", "currency_api_id": "/currency-api-id/uat2", "currency_api_key": "/currency-api-key/uat2", "bloomberg_api_account_id": "/bloomberg_api_account_id/uat2", "bloomberg_api_client_id": "/bloomberg_api_client_id/uat2", "bloomberg_api_client_secret": "/bloomberg_api_client_secret/uat2", "dnv_access_key": "/paris2-dnv-access-key/uat2", "dnv_company_id": "/paris2-dnv-company-id/uat2", "enabled_verifiers": "/paris2-enabled-verifiers/uat2", "paris2-inspection-form-b-secret": "/paris2-inspection-form-b-secret/uat2", "paris2_reference_db_connection": "/paris2-db-connection/reference/uat2", "paris2_reference_db_password": "/paris2-reference-db-password/uat2"}, "swagger": {"authorizationUrl": "https://auth-uat2.fleetship.com/auth/realms/paris2/protocol/openid-connect/auth", "account_url": "https://paris-api-uat2.fleetship.com/account", "data_import_url": "https://dataimport-uat2.fleetship.com/api/data-import"}, "inspection": {"form_b_jwt_public_key": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA308prIqTpQbFv5GMaR9u\ngZFgp0n3/A8pPRW+Q8sXYQp0Hkf8wLrNSBNglAMSXBVSc0LneNT6hor5rp7258An\nQP2cPtELA46wWN6nuOGk5hH6FSTzbCjx1LH7FlrF2VY91XjwQivmPezHnk7e3h1P\nmNV0AipQdsEMgGAeH05bcCUEOj3iyb1wXw+khTlfzy+dCxbnuOsl0RYe8YufWe8+\nDEGa5eADtKA03+3/4ZEK/FM2arYS0PTjX94wl+WPl8Uzz755g2KO2N2A6ipjA7H6\nErfsG1RKCYMMUP8r6R0L76vvv0d+xpDZ39EBuKh50Nzbc8lgFQ9IdCLwBH9jaEmY\nwQIDAQAB\n-----END PUBLIC KEY-----\n"}, "lambda_layer": {"oracle_instant_client": "arn:aws:lambda:ap-southeast-1:************:layer:oracle-instant-client:15", "chromium": "arn:aws:lambda:ap-southeast-1:************:layer:chromium:4"}}