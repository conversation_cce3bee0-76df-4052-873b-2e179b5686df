/**
 * a simple script to compare the csp rules of 2 json files
 */

const fs = require('fs');
const path = require('path');

JSON_OLD_CSP = process.env.JSON_OLD_CSP ?? './csp-check/csp.json';
JSON_NEW_CSP = process.env.JSON_NEW_CSP ?? './csp-check/csp-live.json';

// read the 2 json files, csp.json and csp-live.json
const oldCspJson = require(JSON_OLD_CSP);
const newCspJson = require(JSON_NEW_CSP);

// compare the 2 json files
// 1. compare image-src, find the difference
const imageSrc = oldCspJson['img-src'].filter(src => !newCspJson['img-src'].includes(src));
if (imageSrc.length > 0) {
    console.log('missing img-src:', imageSrc);
} else {
    console.log('img-src is the same');
}

// 2. compare style-src, find the difference
const styleSrc = oldCspJson['style-src'].filter(src => !newCspJson['style-src'].includes(src));
if (styleSrc.length > 0) {
    console.log('missing style-src:', styleSrc);
} else {
    console.log('style-src is the same');
}

// 3. compare default from csp-master.json, default was split into default-src, frame-src
// , connect-src, script-src, object-src, script-src, font-src, worker-src

// combine default-src, frame-src, connect-src, script-src, object-src, script-src, 
// font-src, worker-src from cspLive
const defaultLiveSrc = newCspJson['default-src'].concat(newCspJson['frame-src'], newCspJson['connect-src'],
    newCspJson['script-src'], newCspJson['object-src'], newCspJson['script-src'], newCspJson['font-src'],
    newCspJson['worker-src']);

// compare default-src, find the difference
const defaultSrc = oldCspJson['default-src'].filter(src => !defaultLiveSrc.includes(src));
if (defaultSrc.length > 0) {
    console.log('missing default-src:', defaultSrc);
} else {
    console.log('all old default-src rules have been included in the new rules');
}